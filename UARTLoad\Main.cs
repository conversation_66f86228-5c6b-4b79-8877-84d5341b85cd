using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.IO.Ports;
using System.Threading;



namespace UARTLoad
{
    public partial class Main : Form
    {
        // 厂家协议配置结构
        public class ManufacturerProtocol
        {
            public string Name { get; set; }
            public byte Code { get; set; }        // 厂家代码 1-99
            public byte BaudRateCode { get; set; } // 波特率代码 1-6
            public byte DataBits { get; set; }     // 数据位 7-9
            public byte StopBits { get; set; }     // 停止位 1-2
            public byte Parity { get; set; }       // 校验位 0-2
            public int CollectPeriod { get; set; } // 采集周期(秒) 1-999999
        }

        // 预定义的厂家协议配置
        private Dictionary<int, ManufacturerProtocol> manufacturerProtocols = new Dictionary<int, ManufacturerProtocol>
        {
            {0, new ManufacturerProtocol { Name = "大连道盛", Code = 1, BaudRateCode = 4, DataBits = 8, StopBits = 1, Parity = 0, CollectPeriod = 30 }},
            {1, new ManufacturerProtocol { Name = "泰安", Code = 2, BaudRateCode = 4, DataBits = 8, StopBits = 1, Parity = 0, CollectPeriod = 30 }},
            {2, new ManufacturerProtocol { Name = "唐山", Code = 3, BaudRateCode = 4, DataBits = 8, StopBits = 1, Parity = 0, CollectPeriod = 30 }},
            {3, new ManufacturerProtocol { Name = "河南", Code = 4, BaudRateCode = 4, DataBits = 8, StopBits = 1, Parity = 0, CollectPeriod = 30 }}
        };

        System.Collections.Generic.List<byte[]> array = new List<byte[]>();

        byte[] key = new byte[8];  //密钥
        byte[] fileID = new byte[8];
        byte[] identify = new byte[8] { 0x41, 0x6C, 0x70, 0x68, 0x61, 0x30, 0x30, 0x37 };  //Alpha007
        byte[] reboot = new byte[4] { 0x7E, 0x02, 0x06, 0xCE };

        byte[] upBegin = new byte[14] { 0x7E, 0x00, 0x0C, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xCE };
        byte[] upEnd = new byte[6] { 0x7E, 0x00, 0x04, 0x05, 0x00, 0xCE };

        // 配置相关的数据包
        byte[] configPacket = new byte[64]; // 配置数据包
        byte[] otherConfigPacket = new byte[64]; // 其他配置数据包
        private int otherConfigPacketLength = 0; // 其他配置数据包长度
        
        delegate void UpdateTextEventHandler(string text);  //委托
        delegate void UpdateIPConfigEventHandler(string ip, string port, string period);  //IP配置更新委托
        delegate void UpdateProtocolConfigEventHandler(string collectPeriod, int manufacturerIndex, int baudRateIndex, int dataBitsIndex, int stopBitsIndex, int parityIndex);  //协议配置更新委托
        delegate void UpdateOtherConfigEventHandler(int compatModeIndex);  //其他配置更新委托

        UpdateTextEventHandler updateText1;
        UpdateTextEventHandler updateText2;
        UpdateTextEventHandler updateText3;
        UpdateTextEventHandler updateVision;
        UpdateIPConfigEventHandler updateIPConfig1;
        UpdateIPConfigEventHandler updateIPConfig2;
        UpdateIPConfigEventHandler updateIPConfig3;
        UpdateProtocolConfigEventHandler updateProtocolConfig;
        UpdateOtherConfigEventHandler updateOtherConfig;
        SerialPortOp port = new SerialPortOp();
        getCRC CRC = new getCRC();

        int currentRecvLen = 0;
        int i = 0;

		private bool isConnected = false;
		private System.Windows.Forms.Timer connectionTimer;
		private readonly byte[] connectPacket = new byte[4] { 0x7E, 0x02, 0x02, 0xCE };

        public Main()
        {
            this.StartPosition = FormStartPosition.CenterScreen;//居中
            this.FormBorderStyle = FormBorderStyle.Fixed3D;
            InitializeComponent();

            updateText1 = new UpdateTextEventHandler(UpdateRecvDataTb);  //实例化委托对象
            updateText2 = new UpdateTextEventHandler(UpdateProBar);
            updateText3 = new UpdateTextEventHandler(UpdateConn);
            updateVision = new UpdateTextEventHandler(UpdateVision);
            updateIPConfig1 = new UpdateIPConfigEventHandler(UpdateIPConfig1);
            updateIPConfig2 = new UpdateIPConfigEventHandler(UpdateIPConfig2);
            updateIPConfig3 = new UpdateIPConfigEventHandler(UpdateIPConfig3);
            updateProtocolConfig = new UpdateProtocolConfigEventHandler(UpdateProtocolConfig);
			updateOtherConfig = new UpdateOtherConfigEventHandler(UpdateOtherConfig);

			// 初始化连接定时器（周期发送连接报文）
			connectionTimer = new System.Windows.Forms.Timer();
			connectionTimer.Interval = 1000;
			connectionTimer.Tick += ConnectionTimer_Tick;

			// 为IP输入框添加验证事件
            SetupIPValidation();
        }

		private void ConnectionTimer_Tick(object sender, EventArgs e)
		{
			try
			{
				if (port != null && port.port != null && port.port.IsOpen && !isConnected)
				{
					port.SendData(connectPacket);
				}
				else
				{
					connectionTimer.Stop();
				}
			}
			catch { }
		}

        private void SetupIPValidation()
        {
            // 为所有IP输入框添加KeyPress事件（限制输入字符）
            txt_centerIP1.KeyPress += IPTextBox_KeyPress;
            txt_centerIP2.KeyPress += IPTextBox_KeyPress;
            txt_centerIP3.KeyPress += IPTextBox_KeyPress;

            // 为所有IP输入框添加Leave事件（失去焦点时验证）
            txt_centerIP1.Leave += IPTextBox_Leave;
            txt_centerIP2.Leave += IPTextBox_Leave;
            txt_centerIP3.Leave += IPTextBox_Leave;

            // 为端口输入框添加验证事件
            txt_centerPort1.KeyPress += PortTextBox_KeyPress;
            txt_centerPort2.KeyPress += PortTextBox_KeyPress;
            txt_centerPort3.KeyPress += PortTextBox_KeyPress;

            txt_centerPort1.Leave += PortTextBox_Leave;
            txt_centerPort2.Leave += PortTextBox_Leave;
            txt_centerPort3.Leave += PortTextBox_Leave;

    

            // 为上报周期输入框添加验证事件
            txt_reportPeriod1.KeyPress += ReportPeriodTextBox_KeyPress;
            txt_reportPeriod2.KeyPress += ReportPeriodTextBox_KeyPress;
            txt_reportPeriod3.KeyPress += ReportPeriodTextBox_KeyPress;

            txt_reportPeriod1.Leave += ReportPeriodTextBox_Leave;
            txt_reportPeriod2.Leave += ReportPeriodTextBox_Leave;
            txt_reportPeriod3.Leave += ReportPeriodTextBox_Leave;

            // 为采集周期输入框添加验证事件
            txt_collectPeriod.KeyPress += CollectPeriodTextBox_KeyPress;
            txt_collectPeriod.Leave += CollectPeriodTextBox_Leave;
        }

        private void FormLoad(object sender, EventArgs e)
        {
            port.port.ReceivedBytesThreshold = 1;//设置 DataReceived 事件发生前内部输入缓冲区中的字节数
            port.port.DataReceived += new SerialDataReceivedEventHandler(port_DataReceived);//DataReceived事件委托
        }


        private void UpdateConn(string text)
        {
            isConnected = true;
            btn_sysConn.Text = "已连接";

            // 启用其余功能按钮
            btn_setIP1.Enabled = true;
            btn_readIP1.Enabled = true;
            btn_setIP2.Enabled = true;
            btn_readIP2.Enabled = true;
            btn_setIP3.Enabled = true;
            btn_readIP3.Enabled = true;
            btn_setProtocol.Enabled = true;
            btn_readProtocol.Enabled = true;
            btn_readVision.Enabled = true;

            // 停止周期发送连接包
            if (connectionTimer != null)
            {
                connectionTimer.Stop();
            }
        }

        private void UpdateVision(string text)
        {
            txt_sysVision.Text = text;
        }

        private void UpdateIPConfig1(string ip, string port, string period)
        {
            txt_centerIP1.Text = ip;
            txt_centerPort1.Text = port;
            txt_reportPeriod1.Text = period;
        }

        private void UpdateIPConfig2(string ip, string port, string period)
        {
            txt_centerIP2.Text = ip;
            txt_centerPort2.Text = port;
            txt_reportPeriod2.Text = period;
        }

        private void UpdateIPConfig3(string ip, string port, string period)
        {
            txt_centerIP3.Text = ip;
            txt_centerPort3.Text = port;
            txt_reportPeriod3.Text = period;
        }

        private void UpdateProtocolConfig(string collectPeriod, int manufacturerIndex, int baudRateIndex, int dataBitsIndex, int stopBitsIndex, int parityIndex)
        {
            txt_collectPeriod.Text = collectPeriod;
            cmb_meterManufacturer.SelectedIndex = manufacturerIndex;
            cmb_baudRate.SelectedIndex = baudRateIndex;
            cmb_dataBits.SelectedIndex = dataBitsIndex;
            cmb_stopBits.SelectedIndex = stopBitsIndex;
            cmb_parity.SelectedIndex = parityIndex;
        }

        private void UpdateOtherConfig(int compatModeIndex)
        {
            cmb_dataCompatMode.SelectedIndex = compatModeIndex;
        }

        private void UpdateRecvDataTb(string text)
        {

            txt_recvdata.AppendText( text + "\n");
          
        }

        private void UpdateProBar(string text)
        {
            if (int.Parse(text) <= progressBar.Maximum)
            {
                progressBar.Value += 1;
              
            }
        }

        private void btn_fileOpen_Click(object sender, EventArgs e)
        {
            array.Clear();
            OpenFileDialog fdialog = new OpenFileDialog();
            fdialog.Filter = "二进制文件|*.bin";
            if (fdialog.ShowDialog() == DialogResult.OK)
            {
                this.txt_fileName.Text = fdialog.FileName;
           
                FileStream fs = new FileStream(fdialog.FileName, FileMode.Open, FileAccess.Read);
                BinaryReader reader = new BinaryReader(fs);

                reader.BaseStream.Position = 0x0000;

                fileID = reader.ReadBytes(8);

                if (MemoryCompare(fileID, identify) != 0)
                {
                    MessageBox.Show("非法文件！");
                }
                else
                {
                    key = reader.ReadBytes(8);
                    for (int i = 0; i < 8; i++)
                    {
                        upBegin[5 + i] = key[i];
                    }

                    try
                    {
                        int index = 16;
                        while (fs.Length > index)
                        {
                            int lenght = (int)fs.Length - index < 1024 ? (int)fs.Length - index : 1024;
                            byte[] sBuffer = reader.ReadBytes(lenght);
                            array.Add(sBuffer);
                            index += lenght;
                        }
                    }
                    catch { }
                }
            }
        }

        private void btn_fileDownload_Click(object sender, EventArgs e)
        {
            if (array.Count <= 0)
            {
                MessageBox.Show("请先选择一个文件！");
            }
            else
            {
                progressBar.Maximum = array.Count;
                upBegin[4] = (byte)array.Count;     //发送文件大小  Kb

                if (array.Count > 48)
                {
                    MessageBox.Show("文件大小超过48K!");
                }
                else
                {
                    port.SendData(reboot);   //重启
                    Thread.Sleep(2000);
                    
                    port.SendData(upBegin);
                    i = 0;
                    progressBar.Value = 0;
                }
            }
        }

        private void port_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {

            byte[] sendbuf = new byte[1032];
            string ss = "";
            sendbuf[0] = 0x7E;
            while (port.port.BytesToRead - currentRecvLen > 0)
            {
                currentRecvLen = port.port.BytesToRead;
                System.Threading.Thread.Sleep(50);
                
            }
            currentRecvLen = 0;
            //---------------------------------------------
            try
            {
                int rlen = port.port.BytesToRead;
                
                byte[] buf = new byte[rlen];//声明一个临时数组存储当前来的串口数据 
                port.port.Read(buf, 0, rlen);//读取缓冲数据
                foreach(byte b in buf)
                {
                    ss = ss + b.ToString("X2") + " ";
                }
                Console.WriteLine(ss +   "-----------"+ i.ToString());
                this.Invoke(updateText1, new string[] { ss   +   "   count:"+ i.ToString() });

                // 收到连接成功/心跳响应：7E 03 82 00 CE
                if (rlen == 5 && buf[2] == 0x82 && buf[3] == 0x00)
                {
                    this.Invoke(updateText3, new string[] {""});
                }

                //配置设置成功
                if (rlen == 5 && buf[2] == 0x83)
                {
                    MessageBox.Show("配置设置成功！");
                }

                //配置读取响应和系统版本响应
                if (rlen > 5 && (buf[2] == 0x84 || buf[2] == 0x85 || buf[2] == 0x83 || buf[2] == 0x91 || buf[2] == 0x92 || buf[2] == 0x93 || buf[2] == 0x95 || buf[2] == 0x96))
                {
                    ParseConfigResponse(buf, rlen);
                }

                //系统版本（旧格式，已废弃，现在使用完整帧格式）
                /*
                if (rlen == 4)
                {
                    //if ((byte)(buf[0]& 0x04) ==0x04)
                    //    MessageBox.Show("未检测到GPRS模块！");
                    if ((byte)(buf[0]&0x01) == 0x01)
                        MessageBox.Show("设备异常，读取失败！");
                    //if ((byte)(buf[0]&0x02) ==0x02)
                    //    MessageBox.Show("未检测到语音模块！");

                    string sv = "V" + Convert.ToChar(buf[1]) + "." + Convert.ToChar(buf[2]) + "." + Convert.ToChar(buf[3]);
                    this.Invoke(updateVision, new string[] {  sv });
                }
                */

                //下载系统文件

                if (rlen >= 5 && ((buf[3] == 0x04 && (byte)(buf[4] + 1) == (byte)i) || (buf[4] == 0x04 && (byte)(buf[5] + 1) == (byte)i)))
                {
                    if (i < array.Count)    //i=0   开始发送文件
                    {
                         // upEnd[9] = (byte)(upEnd[9]^GetXOR(array[i], array[i].Length));
                          sendbuf[1] = (byte)((array[i].Length + 6) / 256);   //数据长度
                          sendbuf[2] = (byte)(array[i].Length + 6);
                          sendbuf[3] = 0x03;
                          sendbuf[4] = (byte)i;     //发送帧序号

                          for (int j = 0; j < array[i].Length; j++)
                          {
                              sendbuf[j + 5] = array[i][j];
                          }
                          ushort us_crc16 = CRC.usMBCRC16(sendbuf, (array[i].Length + 5));
                          sendbuf[5 + array[i].Length] = (byte)us_crc16;   //CRC校验位    低位在前高位在后
                          sendbuf[6 + array[i].Length] = (byte)(us_crc16 >> 8);   

                          sendbuf[7 + array[i].Length] = 0xCE;

                          port.port.ReadExisting();
                          port.SendData(sendbuf);

                          // System.Threading.Thread.Sleep(500);
                          // progressBar.Value += 1;
                       
                         this.Invoke(updateText2, new string[] { i.ToString() });
                         i++;
                    }
                    else
                    {
                        upEnd[3] = 0x05;    //文件发送结束
                        port.SendData(upEnd);
                    }
                }
                if (rlen >= 5 && (buf[3] == 0x06 || (buf[1] == 0x7E &&buf[4] == 0x06)))
                {
                    MessageBox.Show("更新成功！");
                   
                }
              
            }
            catch (Exception port_recv_e)
            {
                MessageBox.Show(port_recv_e.Message);
            }
        }

        public static int MemoryCompare(byte[] b1, byte[] b2)
        {
            int result = 0;
            if (b1.Length != b2.Length)
                result = b1.Length - b2.Length;
            else
            {
                for (int i = 0; i < b1.Length; i++)
                {
                    if (b1[i] != b2[i])
                    {
                        result = (int)(b1[i] - b2[i]);
                        break;
                    }
                }
            }
            return result;
        }

        //计算校验位XOR
        public static byte GetXOR(byte[] bytes, int end)
        {
            byte XOR = 0x00;

            if (bytes != null)
            {
                // XOR = 0x00;
                for (int i = 0; i < end; i++)
                {
                    XOR = (byte)(XOR ^ bytes[i]);
                }
            }
            return XOR;
        }

        private void btn_openPort_Click(object sender, EventArgs e)
        {
            string comName = "";

            try
            {
                if (btn_openPort.Text == "打开")
                {
                    comName = com_portName.SelectedItem.ToString();
                    port.InitCOM(comName, 115200, false, true);

                    port.port.ReceivedBytesThreshold = 1;//设置 DataReceived 事件发生前内部输入缓冲区中的字节数
                    port.port.DataReceived += new SerialDataReceivedEventHandler(port_DataReceived);//DataReceived事件委托

                    //if (port.OpenPort())
                    //{
                    port.port.Open();
                    btn_openPort.Text = "关闭";
                    // 打开串口后：仅允许点击“连接”，其余功能禁用，等待连接建立
                    btn_fileDownload.Enabled = true;
                    isConnected = false;
                    btn_sysConn.Enabled = true;

                    btn_setIP1.Enabled = false;
                    btn_readIP1.Enabled = false;
                    btn_setIP2.Enabled = false;
                    btn_readIP2.Enabled = false;
                    btn_setIP3.Enabled = false;
                    btn_readIP3.Enabled = false;
                    btn_setProtocol.Enabled = false;
                    btn_readProtocol.Enabled = false;
                    btn_readVision.Enabled = false;
                    // }
                }
                else
                {
                    //if (port.ClosePort())
                    //{
                    port.port.Close();
                    btn_openPort.Text = "打开";
                    btn_sysConn.Text = "连接";
                    btn_fileDownload.Enabled = false;

                    btn_sysConn.Enabled = false;

                    isConnected = false;
                    if (connectionTimer != null) connectionTimer.Stop();

                    btn_setIP1.Enabled = false;
                    btn_readIP1.Enabled = false;
                    btn_setIP2.Enabled = false;
                    btn_readIP2.Enabled = false;
                    btn_setIP3.Enabled = false;
                    btn_readIP3.Enabled = false;
                    btn_setProtocol.Enabled = false;
                    btn_readProtocol.Enabled = false;
                    btn_readVision.Enabled = false;

                    //}

                }
            }
            catch (IOException)
            {
                port.ClosePort();
                port = new SerialPortOp();
            }

        }



        //USB转串口突然拔出检测    防止程序自动退出
        protected override void DefWndProc(ref Message m)
        {
            if (m.Msg == 0x0219)//WM_DEVICECHANGE    
            {
                try
                {
                    if (port.port.IsOpen)
                    {
                        bool flag = true;
                        //查找所有存在的串口                     
                        string[] portnames = SerialPort.GetPortNames();
                        for (int i = 0; i < portnames.Length; i++)
                        {
                            if (port.port.PortName == portnames[i])
                                flag = false;//不是本串口被拔           
                        }
                        if (flag == true)
                        //所有存在的串口中找不到已经打开的串口               
                        {
                            port.port.Close();
                        }
                    }
                }
                catch
                {
                }
            }
            base.DefWndProc(ref m);
        }

        private void btn_closePort_Click(object sender, EventArgs e)
        {
            port.ClosePort();
        }

        private void com_portName_DropDown(object sender, EventArgs e)
        {
            GetPort();
        }

        void GetPort()               //自动刷新串口
        {
            int ValueCount;
            try
            {
                Microsoft.Win32.RegistryKey hklm = Microsoft.Win32.Registry.LocalMachine;
                Microsoft.Win32.RegistryKey software11 = hklm.OpenSubKey("HARDWARE");
                //打开"HARDWARE"子健
                Microsoft.Win32.RegistryKey software = software11.OpenSubKey("DEVICEMAP");
                Microsoft.Win32.RegistryKey sitekey = software.OpenSubKey("SERIALCOMM");
                //获取当前子健
                String[] Str2 = sitekey.GetValueNames();
                //Str2=System.IO.Ports.SerialPort.GetPortNames()；//第二中方法，直接取得串口值
                //获得当前子健下面所有健组成的字符串数组
                ValueCount = sitekey.ValueCount;
                int i;
                if (ValueCount > 0)
                {

                    com_portName.Items.Clear();
                    for (i = 0; i < ValueCount; i++)
                    {
                        com_portName.Items.Add(sitekey.GetValue(Str2[i]));
                    }
                }
            }
            catch(Exception e)
            {
                MessageBox.Show(e.ToString());
            }

            //获得当前子健存在的健值
           
        }





        private void btn_setIP1_Click(object sender, EventArgs e)  //设置中心1
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                BuildIPPacket(1, txt_centerIP1.Text, txt_centerPort1.Text, txt_reportPeriod1.Text);

                // 直接使用串口发送，绕过SerialPortOp的长度限制
                if (port.port.IsOpen)
                {
                    byte[] sendData = new byte[ipPacketLength];
                    Array.Copy(configPacket, sendData, ipPacketLength);
                    port.port.Write(sendData, 0, ipPacketLength);
                }

                // 显示发送的数据包（用于调试）
                string hexString = "";
                for (int i = 0; i < ipPacketLength; i++)
                {
                    hexString += configPacket[i].ToString("X2") + " ";
                }
                MessageBox.Show("中心1配置已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("中心1配置发送失败：" + ex.Message);
            }
        }

        private void btn_readIP1_Click(object sender, EventArgs e)  //读取中心1
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                byte[] readCmd = new byte[4] { 0x7E, 0x02, 0x11, 0xCE };
                port.SendData(readCmd);
                MessageBox.Show("读取中心1命令已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("读取中心1失败：" + ex.Message);
            }
        }

        private void btn_setIP2_Click(object sender, EventArgs e)  //设置中心2
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                BuildIPPacket(2, txt_centerIP2.Text, txt_centerPort2.Text, txt_reportPeriod2.Text);

                // 直接使用串口发送，绕过SerialPortOp的长度限制
                if (port.port.IsOpen)
                {
                    byte[] sendData = new byte[ipPacketLength];
                    Array.Copy(configPacket, sendData, ipPacketLength);
                    port.port.Write(sendData, 0, ipPacketLength);
                }

                // 显示发送的数据包（用于调试）
                string hexString = "";
                for (int i = 0; i < ipPacketLength; i++)
                {
                    hexString += configPacket[i].ToString("X2") + " ";
                }
                MessageBox.Show("中心2配置已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("中心2配置发送失败：" + ex.Message);
            }
        }

        private void btn_readIP2_Click(object sender, EventArgs e)  //读取中心2
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                byte[] readCmd = new byte[4] { 0x7E, 0x02, 0x12, 0xCE };
                port.SendData(readCmd);
                MessageBox.Show("读取中心2命令已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("读取中心2失败：" + ex.Message);
            }
        }

        private void btn_setIP3_Click(object sender, EventArgs e)  //设置中心3
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                BuildIPPacket(3, txt_centerIP3.Text, txt_centerPort3.Text, txt_reportPeriod3.Text);

                // 直接使用串口发送，绕过SerialPortOp的长度限制
                if (port.port.IsOpen)
                {
                    byte[] sendData = new byte[ipPacketLength];
                    Array.Copy(configPacket, sendData, ipPacketLength);
                    port.port.Write(sendData, 0, ipPacketLength);
                }

                // 显示发送的数据包（用于调试）
                string hexString = "";
                for (int i = 0; i < ipPacketLength; i++)
                {
                    hexString += configPacket[i].ToString("X2") + " ";
                }
                MessageBox.Show("中心3配置已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("中心3配置发送失败：" + ex.Message);
            }
        }

        private void btn_readIP3_Click(object sender, EventArgs e)  //读取中心3
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                byte[] readCmd = new byte[4] { 0x7E, 0x02, 0x13, 0xCE };
                port.SendData(readCmd);
                MessageBox.Show("读取中心3命令已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("读取中心3失败：" + ex.Message);
            }
        }



        private void btn_setProtocol_Click(object sender, EventArgs e)  //设置厂家协议
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                BuildProtocolPacket();

                // 直接使用串口发送，绕过SerialPortOp的长度限制
                if (port.port.IsOpen)
                {
                    byte[] sendData = new byte[protocolPacketLength];
                    Array.Copy(configPacket, sendData, protocolPacketLength);
                    port.port.Write(sendData, 0, protocolPacketLength);
                }

                // 显示发送的数据包（用于调试）
                string hexString = "";
                for (int i = 0; i < protocolPacketLength; i++)
                {
                    hexString += configPacket[i].ToString("X2") + " ";
                }
                MessageBox.Show("厂家协议配置已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("厂家协议配置发送失败：" + ex.Message);
            }
        }

        private void btn_readProtocol_Click(object sender, EventArgs e)  //读取厂家协议
        {
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            try
            {
                byte[] readCmd = new byte[4] { 0x7E, 0x02, 0x15, 0xCE };
                port.SendData(readCmd);
                MessageBox.Show("读取厂家协议命令已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("读取厂家协议失败：" + ex.Message);
            }
        }

        private void btn_readVision_Click(object sender, EventArgs e) //读取系统版本
        {
            //7E 02 05 CE //查询硬件状态
            byte[] hardStatus = new byte[4] { 0x7e, 0x02, 0x05, 0xce };

            // 未连接不允许读取版本
            if (!isConnected)
            {
                MessageBox.Show("请先连接设备！");
                return;
            }

            port.SendData(hardStatus);
        }

        private void btn_sysConn_Click(object sender, EventArgs e)   //连接系统
        {
            // 点击后开始周期发送连接包，等待设备回复
            if (port != null && port.port != null && port.port.IsOpen)
            {
                isConnected = false;

                // UI 状态
                btn_sysConn.Text = "连接中...";
                btn_sysConn.Enabled = false; // 避免重复点击

                // 禁用其他功能按钮直至连接建立
                btn_setIP1.Enabled = false;
                btn_readIP1.Enabled = false;
                btn_setIP2.Enabled = false;
                btn_readIP2.Enabled = false;
                btn_setIP3.Enabled = false;
                btn_readIP3.Enabled = false;
                btn_setProtocol.Enabled = false;
                btn_readProtocol.Enabled = false;
                btn_readVision.Enabled = false;

                // 启动定时器循环发连接包
                if (connectionTimer != null)
                {
                    connectionTimer.Stop();
                    connectionTimer.Start();
                }
            }
        }



        private void BuildIPPacket(int ipIndex, string ipText, string portText, string reportPeriodText)
        {
            // 构建IP配置数据包（包含IP、端口和上报周期）
            // 协议格式：7E + 长度 + 命令码 + 数据 + CRC + CE
            // 新格式：IP地址7字节 + 端口3字节 + 上报周期2字节 = 12字节数据

            int index = 0;
            configPacket[index++] = 0x7E; // 帧头
            configPacket[index++] = 0x00; // 长度高位
            configPacket[index++] = 0x0F; // 长度低位（命令码1 + IP7 + 端口3 + 上报周期2 + CRC2 = 15字节）
            configPacket[index++] = (byte)(0x10 + ipIndex); // 命令码：0x11-0x13

            // IP地址验证和处理
            if (string.IsNullOrEmpty(ipText))
            {
                throw new ArgumentException("IP地址不能为空！");
            }

            if (!IsValidIPAddress(ipText))
            {
                throw new ArgumentException($"IP地址格式不正确：{ipText}");
            }

            // IP地址处理：7字节格式，完整4字节IP（公网支持），两端保留填充位
            string[] ipParts = ipText.Split('.');
            uint ip = (uint)(Convert.ToByte(ipParts[0]) << 24 |
                            Convert.ToByte(ipParts[1]) << 16 |
                            Convert.ToByte(ipParts[2]) << 8 |
                            Convert.ToByte(ipParts[3]));

            // 7字节IP地址布局：00 00 IP1 IP2 IP3 IP4 00
            configPacket[index++] = 0x00; // 最高字节，固定为0（占位）
            configPacket[index++] = 0x00; // 次高字节，固定为0（占位）
            configPacket[index++] = (byte)((ip >> 24) & 0xFF); // IP1（完整8位，支持公网）
            configPacket[index++] = (byte)((ip >> 16) & 0xFF); // IP2
            configPacket[index++] = (byte)((ip >> 8) & 0xFF);  // IP3
            configPacket[index++] = (byte)(ip & 0xFF);         // IP4
            configPacket[index++] = 0x00; // 最低字节，固定为0（占位）

            // 端口验证和处理
            if (string.IsNullOrEmpty(portText))
            {
                throw new ArgumentException("端口号不能为空！");
            }

            if (!IsValidPort(portText))
            {
                throw new ArgumentException($"端口号不正确：{portText}，端口号必须是1-65535之间的数字");
            }

            // 端口处理：3字节格式，取低位，高位不用为零
            ushort port = Convert.ToUInt16(portText);
            configPacket[index++] = (byte)(port & 0xFF);        // 低字节
            configPacket[index++] = (byte)((port >> 8) & 0xFF); // 中字节
            configPacket[index++] = 0x00;                        // 高字节，固定为0

            // 上报周期处理：2字节，十进制，单位分钟，范围0001~9999分钟
            ushort reportPeriodMinutes = 1; // 默认1分钟
            if (!string.IsNullOrEmpty(reportPeriodText))
            {
                if (!ushort.TryParse(reportPeriodText, out reportPeriodMinutes) || reportPeriodMinutes == 0 || reportPeriodMinutes > 9999)
                {
                    throw new ArgumentException($"上报周期不正确：{reportPeriodText}，必须是1-9999之间的数字（分钟）");
                }
            }
            configPacket[index++] = (byte)(reportPeriodMinutes & 0xFF);
            configPacket[index++] = (byte)(reportPeriodMinutes >> 8);

            // 计算CRC
            ushort crc = CRC.usMBCRC16(configPacket, index);
            configPacket[index++] = (byte)(crc & 0xFF);
            configPacket[index++] = (byte)(crc >> 8);

            configPacket[index++] = 0xCE; // 帧尾

            ipPacketLength = index; // 保存IP配置包长度
        }

        private int protocolPacketLength = 0; // 厂家协议包长度变量
        private int ipPacketLength = 0; // IP配置包长度变量

        private void BuildProtocolPacket()
        {
            // 构建厂家协议配置数据包
            // 新格式：3字节采集周期 + 1字节厂家代码 + 1字节波特率 + 1字节数据位 + 1字节停止位 + 1字节校验位 = 8字节数据

            int index = 0;
            configPacket[index++] = 0x7E; // 帧头
            configPacket[index++] = 0x00; // 长度高位
            configPacket[index++] = 0x0A; // 长度低位 (命令码1 + 数据8 + CRC2 = 11字节)
            configPacket[index++] = 0x15; // 设置厂家协议命令码

            // 3字节采集周期，十进制，单位秒，范围000001~999999
            uint collectPeriod = string.IsNullOrEmpty(txt_collectPeriod.Text) ? 30u : Convert.ToUInt32(txt_collectPeriod.Text);
            if (collectPeriod < 1 || collectPeriod > 999999)
            {
                throw new ArgumentException("采集周期必须在1-999999秒范围内");
            }
            configPacket[index++] = (byte)(collectPeriod & 0xFF);
            configPacket[index++] = (byte)((collectPeriod >> 8) & 0xFF);
            configPacket[index++] = (byte)((collectPeriod >> 16) & 0xFF);

            // 1字节水表厂家代码，十进制，范围01~99
            if (manufacturerProtocols.ContainsKey(cmb_meterManufacturer.SelectedIndex))
            {
                configPacket[index++] = manufacturerProtocols[cmb_meterManufacturer.SelectedIndex].Code;
            }
            else
            {
                configPacket[index++] = 1; // 默认大连道盛
            }

            // 1字节波特率代码，1-6
            configPacket[index++] = (byte)(cmb_baudRate.SelectedIndex + 1);

            // 1字节数据位，07-7位，08-8位，09-9位
            byte dataBits = 8; // 默认8位
            if (cmb_dataBits.SelectedIndex == 0) dataBits = 7;
            else if (cmb_dataBits.SelectedIndex == 1) dataBits = 8;
            else if (cmb_dataBits.SelectedIndex == 2) dataBits = 9;
            configPacket[index++] = dataBits;

            // 1字节停止位，1-1位，2-2位
            configPacket[index++] = (byte)(cmb_stopBits.SelectedIndex + 1);

            // 1字节校验位，0-无校验，1-奇校验，2-偶校验
            configPacket[index++] = (byte)cmb_parity.SelectedIndex;

            // 计算CRC
            ushort crc = CRC.usMBCRC16(configPacket, index - 1);
            configPacket[index++] = (byte)(crc & 0xFF);
            configPacket[index++] = (byte)(crc >> 8);

            configPacket[index++] = 0xCE; // 帧尾

            protocolPacketLength = index; // 保存实际包长度
        }

        // 设置其他配置按钮点击事件
        private void btn_setOtherConfig_Click(object sender, EventArgs e)
        {
            try
            {
                BuildOtherConfigPacket();

                // 直接使用串口发送
                if (port.port.IsOpen)
                {
                    byte[] sendData = new byte[otherConfigPacketLength];
                    Array.Copy(otherConfigPacket, sendData, otherConfigPacketLength);
                    port.port.Write(sendData, 0, otherConfigPacketLength);
                }

                MessageBox.Show("配置已发送！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("配置发送失败：" + ex.Message);
            }
        }

        // 读取其他配置按钮点击事件
        private void btn_readOtherConfig_Click(object sender, EventArgs e)
        {
            try
            {
                // 构建读取其他配置的指令
                byte[] readCmd = new byte[4] { 0x7E, 0x02, 0x16, 0xCE }; // 命令码0x16用于读取其他配置

                if (port.port.IsOpen)
                {
                    port.port.Write(readCmd, 0, readCmd.Length);
                    MessageBox.Show("读取其他配置指令已发送！");
                }
                else
                {
                    MessageBox.Show("串口未打开！");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("读取其他配置失败：" + ex.Message);
            }
        }

        // 构建其他配置数据包
        private void BuildOtherConfigPacket()
        {
            // 构建其他配置数据包
            // 协议格式：7E + 长度 + 命令码(0x16) + 数据兼容模式(1字节) + CRC + CE

            int index = 0;
            otherConfigPacket[index++] = 0x7E; // 帧头
            otherConfigPacket[index++] = 0x00; // 长度高位
            otherConfigPacket[index++] = 0x05; // 长度低位 (命令码1 + 数据1 + CRC2 = 4字节)
            otherConfigPacket[index++] = 0x16; // 设置其他配置命令码

            // 数据兼容模式：00=关闭，01=开启
            byte compatMode = (byte)cmb_dataCompatMode.SelectedIndex; // 0或1
            otherConfigPacket[index++] = compatMode;

            // 计算CRC
            ushort crc = CRC.usMBCRC16(otherConfigPacket, index - 1);
            otherConfigPacket[index++] = (byte)(crc & 0xFF);
            otherConfigPacket[index++] = (byte)(crc >> 8);

            otherConfigPacket[index++] = 0xCE; // 帧尾

            otherConfigPacketLength = index; // 保存实际包长度
        }


        private void ParseConfigResponse(byte[] buf, int length)
        {
            try
            {
                byte cmdCode = buf[2]; // 命令码
                int index = 3; // 跳过帧头、长度、命令码

                switch (cmdCode)
                {
                    case 0x83: // 配置设置成功响应
                        byte status = buf[index];
                        if (status == 0)
                        {
                            MessageBox.Show("配置设置成功！", "设置成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show($"配置设置失败！错误码：{status}", "设置失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                        break;

                    case 0x85: // 系统版本响应
                        ParseSystemVersionResponse(buf, index);
                        break;

                    case 0x90: // 上报周期响应
                        ushort reportPeriod = (ushort)(buf[index] | (buf[index + 1] << 8));
                        MessageBox.Show("上报周期读取成功：" + reportPeriod.ToString() + "秒");
                        break;

                    case 0x91: // 中心1响应
                        ParseIPResponse(buf, index, txt_centerIP1, txt_centerPort1, txt_reportPeriod1);
                        MessageBox.Show("中心1读取成功！");
                        break;

                    case 0x92: // 中心2响应
                        ParseIPResponse(buf, index, txt_centerIP2, txt_centerPort2, txt_reportPeriod2);
                        MessageBox.Show("中心2读取成功！");
                        break;

                    case 0x93: // 中心3响应
                        ParseIPResponse(buf, index, txt_centerIP3, txt_centerPort3, txt_reportPeriod3);
                        MessageBox.Show("中心3读取成功！");
                        break;

                    case 0x95: // 厂家协议响应
                        ParseProtocolResponse(buf, index);
                        MessageBox.Show("厂家协议读取成功！");
                        break;

                    case 0x96: // 其他配置响应
                        ParseOtherConfigResponse(buf, index);
                        MessageBox.Show("其他配置读取成功！");
                        break;

                    default:
                        MessageBox.Show("未知的配置响应！");
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("配置解析失败：" + ex.Message);
            }
        }

        private void ParseIPResponse(byte[] buf, int index, TextBox ipTextBox, TextBox portTextBox, TextBox reportPeriodTextBox)
        {
            try
            {
                // 解析7字节IP地址格式（公网支持）：00 00 IP1 IP2 IP3 IP4 00
                // 跳过前2个占位字节（固定为0）
                index += 2;

                // 解析IP地址：公网完整4字节
                byte ip1 = buf[index];
                byte ip2 = buf[index + 1];
                byte ip3 = buf[index + 2];
                byte ip4 = buf[index + 3];

                string ip = ip1 + "." + ip2 + "." + ip3 + "." + ip4;
                index += 5; // 跳过7字节IP地址的剩余部分

                // 解析3字节端口格式
                // 从调试结果看，方式1(小端前2)得到8016，最接近期望的8080
                // 8016 = 0x1F50, 8080 = 0x1F90，差值0x40=64
                // 可能需要加上某个偏移量，或者这就是正确值
                ushort port = (ushort)(buf[index] | (buf[index + 1] << 8)); // 小端序前2字节

                // 如果确实应该是8080而不是8016，可能需要调整
                // 暂时先用解析出的值，看看设备实际使用的是什么端口
                index += 3; // 跳过3字节端口

                // 解析2字节上报周期（分钟）
                ushort reportPeriodMinutes = (ushort)(buf[index] | (buf[index + 1] << 8));

                // 根据不同的TextBox使用对应的委托
                if (ipTextBox == txt_centerIP1)
                {
                    this.Invoke(updateIPConfig1, new object[] { ip, port.ToString(), reportPeriodMinutes.ToString() });
                }
                else if (ipTextBox == txt_centerIP2)
                {
                    this.Invoke(updateIPConfig2, new object[] { ip, port.ToString(), reportPeriodMinutes.ToString() });
                }
                else if (ipTextBox == txt_centerIP3)
                {
                    this.Invoke(updateIPConfig3, new object[] { ip, port.ToString(), reportPeriodMinutes.ToString() });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("解析IP配置响应失败：" + ex.Message, "解析错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ParseProtocolResponse(byte[] buf, int index)
        {
            try
            {
                // 解析新格式的厂家协议配置
                // 3字节采集周期 + 1字节厂家代码 + 1字节波特率 + 1字节数据位 + 1字节停止位 + 1字节校验位 + 4字节备用

                // 解析3字节采集周期
                uint collectPeriod = (uint)(buf[index] | (buf[index + 1] << 8) | (buf[index + 2] << 16));
                index += 3;

                // 解析1字节厂家代码，找到对应的厂家
                byte manufacturerCode = buf[index++];
                int manufacturerIndex = 0;
                for (int i = 0; i < manufacturerProtocols.Count; i++)
                {
                    if (manufacturerProtocols[i].Code == manufacturerCode)
                    {
                        manufacturerIndex = i;
                        break;
                    }
                }

                // 解析1字节波特率代码
                byte baudRateCode = buf[index++];
                int baudRateIndex = (baudRateCode >= 1 && baudRateCode <= 6) ? baudRateCode - 1 : 3; // 默认9600

                // 解析1字节数据位
                byte dataBits = buf[index++];
                int dataBitsIndex = 1; // 默认8位
                if (dataBits == 7) dataBitsIndex = 0;
                else if (dataBits == 8) dataBitsIndex = 1;
                else if (dataBits == 9) dataBitsIndex = 2;

                // 解析1字节停止位
                byte stopBits = buf[index++];
                int stopBitsIndex = (stopBits >= 1 && stopBits <= 2) ? stopBits - 1 : 0; // 默认1位

                // 解析1字节校验位
                byte parity = buf[index++];
                int parityIndex = (parity <= 2) ? parity : 0; // 默认无校验

                // 使用委托安全地更新UI控件
                this.Invoke(updateProtocolConfig, new object[] { collectPeriod.ToString(), manufacturerIndex, baudRateIndex, dataBitsIndex, stopBitsIndex, parityIndex });

                // 跳过4字节备用字段
                index += 4;
            }
            catch (Exception ex)
            {
                MessageBox.Show("解析厂家协议响应失败：" + ex.Message, "解析错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 解析其他配置响应
        private void ParseOtherConfigResponse(byte[] buf, int index)
        {
            try
            {
                // 解析数据兼容模式
                byte compatMode = buf[index];

                // 使用委托安全地更新UI控件
                if (compatMode <= 1)
                {
                    this.Invoke(updateOtherConfig, new object[] { (int)compatMode });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("解析其他配置响应失败：" + ex.Message, "解析错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 解析系统版本响应
        private void ParseSystemVersionResponse(byte[] buf, int index)
        {
            try
            {
                // 解析状态字节
                byte status = buf[index++];
                string statusText = (status == 0) ? "正常" : "异常";

                // 解析版本信息（3个字符）
                char ver1 = (char)buf[index++];
                char ver2 = (char)buf[index++];
                char ver3 = (char)buf[index++];

                string version = $"V{ver1}.{ver2}.{ver3}";

                // 更新界面上的系统版本显示
                this.Invoke(updateVision, new string[] { version });

    
                if(status != 0)
                {
                    MessageBox.Show("设备异常，请检查设备！");
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show($"解析系统版本响应失败：{ex.Message}", "解析错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        static bool IsNumeric(string str)
        {
            System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[0-9]\d*$");
            return reg1.IsMatch(str);
        }

        // IP输入框按键事件 - 限制只能输入数字和点号
        private void IPTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 允许数字、点号、退格键、删除键
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != '.' && e.KeyChar != '\b' && e.KeyChar != (char)Keys.Delete)
            {
                e.Handled = true;
                return;
            }

            TextBox textBox = sender as TextBox;
            string currentText = textBox.Text;

            // 限制点号的输入
            if (e.KeyChar == '.')
            {
                // 不能以点号开头
                if (currentText.Length == 0)
                {
                    e.Handled = true;
                    return;
                }

                // 不能连续输入点号
                if (currentText.EndsWith("."))
                {
                    e.Handled = true;
                    return;
                }

                // 最多只能有3个点号
                if (currentText.Split('.').Length >= 4)
                {
                    e.Handled = true;
                    return;
                }
            }
        }

        // IP输入框失去焦点事件 - 验证IP格式
        private void IPTextBox_Leave(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            string ipText = textBox.Text.Trim();

            if (string.IsNullOrEmpty(ipText))
            {
                MessageBox.Show("IP地址不能为空！\n请输入正确的IP地址格式，例如：*************",
                    "IP地址不能为空", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                return;
            }

            if (!IsValidIPAddress(ipText))
            {
                MessageBox.Show($"IP地址格式不正确：{ipText}\n请输入正确的IP地址格式，例如：*************",
                    "IP地址格式错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                textBox.SelectAll();
            }
        }

        // 验证IP地址格式
        private bool IsValidIPAddress(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress))
                return false;

            string[] parts = ipAddress.Split('.');

            // 必须有4个部分
            if (parts.Length != 4)
                return false;

            foreach (string part in parts)
            {
                // 每部分不能为空
                if (string.IsNullOrEmpty(part))
                    return false;

                // 每部分必须是数字
                if (!int.TryParse(part, out int num))
                    return false;

                // 每部分必须在0-255范围内
                if (num < 0 || num > 255)
                    return false;

                // 不能有前导零（除了单独的0）
                if (part.Length > 1 && part.StartsWith("0"))
                    return false;
            }

            return true;
        }

        // 端口输入框按键事件 - 限制只能输入数字
        private void PortTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 只允许数字和退格键
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != '\b' && e.KeyChar != (char)Keys.Delete)
            {
                e.Handled = true;
            }
        }

        // 端口输入框失去焦点事件 - 验证端口范围
        private void PortTextBox_Leave(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            string portText = textBox.Text.Trim();

            if (string.IsNullOrEmpty(portText))
            {
                MessageBox.Show("端口号不能为空！\n端口号必须是1-65535之间的数字",
                    "端口号不能为空", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                return;
            }

            if (!IsValidPort(portText))
            {
                MessageBox.Show($"端口号不正确：{portText}\n端口号必须是1-65535之间的数字",
                    "端口号错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                textBox.SelectAll();
            }
        }

        // 验证端口号
        private bool IsValidPort(string portText)
        {
            if (string.IsNullOrEmpty(portText))
                return false;

            if (!int.TryParse(portText, out int port))
                return false;

            // 端口号范围：1-65535
            return port >= 1 && port <= 65535;
        }

        // 上报周期输入框按键事件 - 限制只能输入数字
        private void ReportPeriodTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 只允许数字和退格键
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != '\b' && e.KeyChar != (char)Keys.Delete)
            {
                e.Handled = true;
            }
        }

        // 上报周期输入框失去焦点事件 - 验证上报周期
        private void ReportPeriodTextBox_Leave(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            string periodText = textBox.Text.Trim();

            if (string.IsNullOrEmpty(periodText))
            {
                MessageBox.Show("上报周期不能为空！\n上报周期必须是1-9999之间的数字（分钟）",
                    "上报周期不能为空", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                return;
            }

            if (!IsValidReportPeriod(periodText))
            {
                MessageBox.Show($"上报周期不正确：{periodText}\n上报周期必须是1-9999之间的数字（分钟）",
                    "上报周期错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                textBox.Focus();
                textBox.SelectAll();
            }
        }

        // 验证上报周期
        private bool IsValidReportPeriod(string periodText)
        {
            if (string.IsNullOrEmpty(periodText))
                return false;

            if (!ushort.TryParse(periodText, out ushort period))
                return false;

            // 上报周期必须在1-9999分钟范围内
            return period >= 1 && period <= 9999;
        }

        // 验证采集周期
        private bool IsValidCollectPeriod(string periodText)
        {
            if (string.IsNullOrEmpty(periodText))
                return false;

            if (!uint.TryParse(periodText, out uint period))
                return false;

            // 采集周期必须在1-999999秒范围内
            return period >= 1 && period <= 999999;
        }

        // 采集周期输入框按键事件 - 限制只能输入数字
        private void CollectPeriodTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 只允许数字和退格键
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != '\b' && e.KeyChar != (char)Keys.Delete)
            {
                e.Handled = true;
            }
        }

        // 采集周期输入框失去焦点事件 - 验证采集周期
        private void CollectPeriodTextBox_Leave(object sender, EventArgs e)
        {
            string periodText = txt_collectPeriod.Text.Trim();

            if (string.IsNullOrEmpty(periodText))
            {
                MessageBox.Show("采集周期不能为空！\n采集周期必须是1-999999之间的数字（秒）",
                    "采集周期不能为空", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_collectPeriod.Focus();
                return;
            }

            if (!IsValidCollectPeriod(periodText))
            {
                MessageBox.Show($"采集周期不正确：{periodText}\n采集周期必须是1-999999之间的数字（秒）",
                    "采集周期错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_collectPeriod.Focus();
                txt_collectPeriod.SelectAll();
            }
        }

        private void com_portName_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void cmb_meterManufacturer_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 当选择厂家时，自动更新其他协议配置项
            int selectedIndex = cmb_meterManufacturer.SelectedIndex;

            if (manufacturerProtocols.ContainsKey(selectedIndex))
            {
                ManufacturerProtocol protocol = manufacturerProtocols[selectedIndex];

                // 更新波特率（显示代码-值格式）
                cmb_baudRate.SelectedIndex = protocol.BaudRateCode - 1; // 代码1-6对应索引0-5

                // 更新数据位（显示代码-位数格式）
                if (protocol.DataBits == 7) cmb_dataBits.SelectedIndex = 0;
                else if (protocol.DataBits == 8) cmb_dataBits.SelectedIndex = 1;
                else if (protocol.DataBits == 9) cmb_dataBits.SelectedIndex = 2;

                // 更新停止位（显示代码-位数格式）
                cmb_stopBits.SelectedIndex = protocol.StopBits - 1; // 1-2对应索引0-1

                // 更新校验位（显示代码-说明格式）
                cmb_parity.SelectedIndex = protocol.Parity; // 0-2对应索引0-2

                // 更新采集周期
                txt_collectPeriod.Text = protocol.CollectPeriod.ToString();
            }
        }

        private void cmb_baudRate_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 波特率选择变更事件处理（如果需要特殊处理可以在这里添加）
        }

        private void label_centerIP3_Click(object sender, EventArgs e)
        {

        }

        private void txt_sysVision_TextChanged(object sender, EventArgs e)
        {

        }

        private void groupBox6_Enter(object sender, EventArgs e)
        {

        }

        private void label_centerIP1_Click(object sender, EventArgs e)
        {

        }

        private void label_reportPeriod1_Click(object sender, EventArgs e)
        {

        }

        private void txt_centerPort1_TextChanged(object sender, EventArgs e)
        {

        }

        private void txt_centerIP3_TextChanged(object sender, EventArgs e)
        {

        }

        private void label_centerIP4_Click(object sender, EventArgs e)
        {

        }

        private void txt_centerIP4_TextChanged(object sender, EventArgs e)
        {

        }

        private void label_centerPort4_Click(object sender, EventArgs e)
        {

        }

        private void txt_centerPort4_TextChanged(object sender, EventArgs e)
        {

        }

        private void label_reportPeriod4_Click(object sender, EventArgs e)
        {

        }

        private void label_centerIP2_Click(object sender, EventArgs e)
        {

        }

        private void label_meterManufacturer_Click(object sender, EventArgs e)
        {

        }
    }
}
